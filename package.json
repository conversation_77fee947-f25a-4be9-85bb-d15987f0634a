{"name": "product-service", "version": "1.0.0", "description": "Product API for project Atelier", "main": "index.js", "scripts": {"server-dev": "npx nodemon --watch server server/index.js", "server-test": "npx nodemon --watch server -r newrelic server/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/SDC-6-DCJ/Product-Service.git"}, "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/SDC-6-DCJ/Product-Service/issues"}, "homepage": "https://github.com/SDC-6-DCJ/Product-Service#readme", "dependencies": {"dotenv": "^16.0.3", "express": "^4.18.2", "mongoose": "^6.9.1", "morgan": "^1.10.0", "newrelic": "^9.10.1", "nodemon": "^2.0.20", "pg": "^8.9.0"}, "devDependencies": {"eslint": "^8.34.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.27.5"}}